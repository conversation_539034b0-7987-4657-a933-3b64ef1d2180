package com.lisong.mapper;

import com.lisong.entity.Role;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 角色数据访问接口
 */
@Mapper
public interface RoleMapper {
    
    /**
     * 根据角色名查找角色
     */
    Role findByName(@Param("name") String name);
    
    /**
     * 根据用户ID查找用户的所有角色
     */
    List<Role> findByUserId(@Param("userId") Long userId);
    
    /**
     * 查询所有角色
     */
    List<Role> findAll();
    
    /**
     * 为用户分配角色
     */
    int assignRoleToUser(@Param("userId") Long userId, @Param("roleId") Long roleId);
    
    /**
     * 移除用户的角色
     */
    int removeRoleFromUser(@Param("userId") Long userId, @Param("roleId") Long roleId);
}
