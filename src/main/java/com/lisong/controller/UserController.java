package com.lisong.controller;

import com.lisong.dto.ApiResponse;
import com.lisong.dto.RegisterRequest;
import com.lisong.dto.UserResponse;
import com.lisong.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 用户管理控制器
 */
@RestController
@RequestMapping("/api/user")
@Validated
public class UserController {

    @Autowired
    private UserService userService;

    /**
     * 获取用户信息
     */
    @GetMapping("/{id}")
    public ApiResponse<UserResponse> getUserById(@PathVariable Long id) {
        try {
            UserResponse userResponse = userService.findById(id);
            return ApiResponse.success(userResponse);
        } catch (Exception e) {
            return ApiResponse.error(e.getMessage());
        }
    }

    /**
     * 更新用户信息
     */
    @PutMapping("/{id}")
    public ApiResponse<UserResponse> updateUser(@PathVariable Long id, 
                                               @Valid @RequestBody RegisterRequest request) {
        try {
            UserResponse userResponse = userService.updateUser(id, request);
            return ApiResponse.success("更新成功", userResponse);
        } catch (Exception e) {
            return ApiResponse.error(e.getMessage());
        }
    }
}
