package com.lisong.dto;

import com.lisong.entity.Gender;
import lombok.Data;
import jakarta.validation.constraints.*;
import java.time.LocalDate;

/**
 * 注册请求DTO
 */
@Data
public class RegisterRequest {
    @NotBlank(message = "用户名不能为空")
    @Size(min = 3, max = 50, message = "用户名长度必须在3-50个字符之间")
    private String username;
    
    @NotBlank(message = "昵称不能为空")
    @Size(min = 1, max = 100, message = "昵称长度必须在1-100个字符之间")
    private String nickname;
    
    @NotBlank(message = "密码不能为空")
    @Size(min = 6, max = 100, message = "密码长度必须在6-100个字符之间")
    private String password;
    
    @NotBlank(message = "邮箱不能为空")
    @Email(message = "邮箱格式不正确")
    private String email;
    
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;
    
    private Gender gender;
    
    private String avatar;
    
    @Past(message = "出生日期必须是过去的日期")
    private LocalDate birthDate;
}
