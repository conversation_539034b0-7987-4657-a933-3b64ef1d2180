package com.lisong.dto;

import com.lisong.entity.Gender;
import com.lisong.entity.Role;
import lombok.Data;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户响应DTO
 */
@Data
public class UserResponse {
    private Long id;
    private String username;
    private String nickname;
    private String email;
    private String phone;
    private Gender gender;
    private String avatar;
    private LocalDate birthDate;
    private Boolean enabled;
    private LocalDateTime createdAt;
    private List<Role> roles;
}
