package com.lisong.entity;

import lombok.Data;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户实体
 */
@Data
public class User {
    private Long id;
    private String username;
    private String nickname;
    private String password;
    private String email;
    private String phone;
    private Gender gender;
    private String avatar;
    private LocalDate birthDate;
    private Boolean enabled;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    // 用户角色列表
    private List<Role> roles;
}
