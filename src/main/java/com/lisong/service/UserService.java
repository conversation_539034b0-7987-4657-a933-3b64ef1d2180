package com.lisong.service;

import com.lisong.dto.RegisterRequest;
import com.lisong.dto.UserResponse;
import com.lisong.entity.User;
import java.util.List;

/**
 * 用户业务逻辑接口
 */
public interface UserService {
    
    /**
     * 用户注册
     */
    UserResponse register(RegisterRequest request);
    
    /**
     * 根据用户名查找用户
     */
    User findByUsername(String username);
    
    /**
     * 根据ID查找用户
     */
    UserResponse findById(Long id);
    
    /**
     * 更新用户信息
     */
    UserResponse updateUser(Long id, RegisterRequest request);
    
    /**
     * 删除用户
     */
    void deleteUser(Long id);
    
    /**
     * 获取所有用户
     */
    List<UserResponse> getAllUsers();
    
    /**
     * 检查用户名是否存在
     */
    boolean existsByUsername(String username);
    
    /**
     * 检查邮箱是否存在
     */
    boolean existsByEmail(String email);
    
    /**
     * 将User实体转换为UserResponse
     */
    UserResponse convertToResponse(User user);
}
