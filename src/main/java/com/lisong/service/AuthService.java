package com.lisong.service;

import com.lisong.dto.LoginRequest;
import com.lisong.dto.UserResponse;

/**
 * 认证业务逻辑接口
 */
public interface AuthService {
    
    /**
     * 用户登录
     */
    UserResponse login(LoginRequest request);
    
    /**
     * 验证密码
     */
    boolean validatePassword(String rawPassword, String encodedPassword);
    
    /**
     * 加密密码
     */
    String encodePassword(String rawPassword);
}
