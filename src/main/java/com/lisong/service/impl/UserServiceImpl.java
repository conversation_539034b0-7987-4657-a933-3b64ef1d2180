package com.lisong.service.impl;

import com.lisong.dto.RegisterRequest;
import com.lisong.dto.UserResponse;
import com.lisong.entity.Role;
import com.lisong.entity.User;
import com.lisong.mapper.RoleMapper;
import com.lisong.mapper.UserMapper;
import com.lisong.service.AuthService;
import com.lisong.service.UserService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户业务逻辑实现
 */
@Service
@Transactional
public class UserServiceImpl implements UserService {

    @Autowired
    private UserMapper userMapper;
    
    @Autowired
    private RoleMapper roleMapper;
    
    @Autowired
    private AuthService authService;

    @Override
    public UserResponse register(RegisterRequest request) {
        // 检查用户名是否已存在
        if (userMapper.existsByUsername(request.getUsername())) {
            throw new RuntimeException("用户名已存在");
        }
        
        // 检查邮箱是否已存在
        if (userMapper.existsByEmail(request.getEmail())) {
            throw new RuntimeException("邮箱已存在");
        }
        
        // 创建新用户
        User user = new User();
        BeanUtils.copyProperties(request, user);
        user.setPassword(authService.encodePassword(request.getPassword()));
        user.setEnabled(true);
        
        // 保存用户
        userMapper.insert(user);
        
        // 分配默认角色（普通用户）
        Role userRole = roleMapper.findByName("ROLE_USER");
        if (userRole != null) {
            roleMapper.assignRoleToUser(user.getId(), userRole.getId());
        }
        
        // 重新查询用户（包含角色信息）
        User savedUser = userMapper.findById(user.getId());
        return convertToResponse(savedUser);
    }

    @Override
    public User findByUsername(String username) {
        return userMapper.findByUsername(username);
    }

    @Override
    public UserResponse findById(Long id) {
        User user = userMapper.findById(id);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }
        return convertToResponse(user);
    }

    @Override
    public UserResponse updateUser(Long id, RegisterRequest request) {
        User existingUser = userMapper.findById(id);
        if (existingUser == null) {
            throw new RuntimeException("用户不存在");
        }
        
        // 检查邮箱是否被其他用户使用
        User userWithEmail = userMapper.findByEmail(request.getEmail());
        if (userWithEmail != null && !userWithEmail.getId().equals(id)) {
            throw new RuntimeException("邮箱已被其他用户使用");
        }
        
        // 更新用户信息（不更新密码和用户名）
        existingUser.setNickname(request.getNickname());
        existingUser.setEmail(request.getEmail());
        existingUser.setPhone(request.getPhone());
        existingUser.setGender(request.getGender());
        existingUser.setAvatar(request.getAvatar());
        existingUser.setBirthDate(request.getBirthDate());
        
        userMapper.update(existingUser);
        
        // 重新查询用户
        User updatedUser = userMapper.findById(id);
        return convertToResponse(updatedUser);
    }

    @Override
    public void deleteUser(Long id) {
        if (userMapper.findById(id) == null) {
            throw new RuntimeException("用户不存在");
        }
        userMapper.deleteById(id);
    }

    @Override
    public List<UserResponse> getAllUsers() {
        List<User> users = userMapper.findAll();
        return users.stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());
    }

    @Override
    public boolean existsByUsername(String username) {
        return userMapper.existsByUsername(username);
    }

    @Override
    public boolean existsByEmail(String email) {
        return userMapper.existsByEmail(email);
    }

    @Override
    public UserResponse convertToResponse(User user) {
        if (user == null) {
            return null;
        }
        
        UserResponse response = new UserResponse();
        BeanUtils.copyProperties(user, response);
        return response;
    }
}
