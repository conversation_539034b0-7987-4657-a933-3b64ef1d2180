<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lisong.mapper.UserMapper">

    <resultMap id="UserResultMap" type="com.lisong.entity.User">
        <id property="id" column="id"/>
        <result property="username" column="username"/>
        <result property="nickname" column="nickname"/>
        <result property="password" column="password"/>
        <result property="email" column="email"/>
        <result property="phone" column="phone"/>
        <result property="gender" column="gender" typeHandler="org.apache.ibatis.type.EnumTypeHandler"/>
        <result property="avatar" column="avatar"/>
        <result property="birthDate" column="birth_date"/>
        <result property="enabled" column="enabled"/>
        <result property="createdAt" column="created_at"/>
        <result property="updatedAt" column="updated_at"/>
        <collection property="roles" ofType="com.lisong.entity.Role">
            <id property="id" column="role_id"/>
            <result property="name" column="role_name"/>
            <result property="description" column="role_description"/>
        </collection>
    </resultMap>

    <select id="findByUsername" resultMap="UserResultMap">
        SELECT u.*, r.id as role_id, r.name as role_name, r.description as role_description
        FROM users u
        LEFT JOIN user_roles ur ON u.id = ur.user_id
        LEFT JOIN roles r ON ur.role_id = r.id
        WHERE u.username = #{username}
    </select>

    <select id="findByEmail" resultMap="UserResultMap">
        SELECT u.*, r.id as role_id, r.name as role_name, r.description as role_description
        FROM users u
        LEFT JOIN user_roles ur ON u.id = ur.user_id
        LEFT JOIN roles r ON ur.role_id = r.id
        WHERE u.email = #{email}
    </select>

    <select id="findById" resultMap="UserResultMap">
        SELECT u.*, r.id as role_id, r.name as role_name, r.description as role_description
        FROM users u
        LEFT JOIN user_roles ur ON u.id = ur.user_id
        LEFT JOIN roles r ON ur.role_id = r.id
        WHERE u.id = #{id}
    </select>

    <select id="findAll" resultMap="UserResultMap">
        SELECT u.*, r.id as role_id, r.name as role_name, r.description as role_description
        FROM users u
        LEFT JOIN user_roles ur ON u.id = ur.user_id
        LEFT JOIN roles r ON ur.role_id = r.id
        ORDER BY u.created_at DESC
    </select>

    <select id="existsByUsername" resultType="boolean">
        SELECT COUNT(*) > 0 FROM users WHERE username = #{username}
    </select>

    <select id="existsByEmail" resultType="boolean">
        SELECT COUNT(*) > 0 FROM users WHERE email = #{email}
    </select>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO users (username, nickname, password, email, phone, gender, avatar, birth_date, enabled)
        VALUES (#{username}, #{nickname}, #{password}, #{email}, #{phone}, #{gender}, #{avatar}, #{birthDate}, #{enabled})
    </insert>

    <update id="update">
        UPDATE users SET
            nickname = #{nickname},
            email = #{email},
            phone = #{phone},
            gender = #{gender},
            avatar = #{avatar},
            birth_date = #{birthDate},
            enabled = #{enabled},
            updated_at = CURRENT_TIMESTAMP
        WHERE id = #{id}
    </update>

    <delete id="deleteById">
        DELETE FROM users WHERE id = #{id}
    </delete>

</mapper>
