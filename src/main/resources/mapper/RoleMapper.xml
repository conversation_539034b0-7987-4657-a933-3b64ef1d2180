<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lisong.mapper.RoleMapper">

    <resultMap id="RoleResultMap" type="com.lisong.entity.Role">
        <id property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="description" column="description"/>
        <result property="createdAt" column="created_at"/>
        <result property="updatedAt" column="updated_at"/>
    </resultMap>

    <select id="findByName" resultMap="RoleResultMap">
        SELECT * FROM roles WHERE name = #{name}
    </select>

    <select id="findByUserId" resultMap="RoleResultMap">
        SELECT r.* FROM roles r
        INNER JOIN user_roles ur ON r.id = ur.role_id
        WHERE ur.user_id = #{userId}
    </select>

    <select id="findAll" resultMap="RoleResultMap">
        SELECT * FROM roles ORDER BY id
    </select>

    <insert id="assignRoleToUser">
        INSERT INTO user_roles (user_id, role_id) VALUES (#{userId}, #{roleId})
    </insert>

    <delete id="removeRoleFromUser">
        DELETE FROM user_roles WHERE user_id = #{userId} AND role_id = #{roleId}
    </delete>

</mapper>
