<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户登录注册系统</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .error {
            color: red;
            margin-top: 5px;
        }
        .success {
            color: green;
            margin-top: 5px;
        }
        .tab {
            display: inline-block;
            padding: 10px 20px;
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            cursor: pointer;
            margin-right: 5px;
        }
        .tab.active {
            background-color: #007bff;
            color: white;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
    </style>
</head>
<body>
    <h1>用户登录注册系统</h1>
    
    <div class="container">
        <div class="tab active" onclick="showTab('login')">登录</div>
        <div class="tab" onclick="showTab('register')">注册</div>
        
        <!-- 登录表单 -->
        <div id="login" class="tab-content active">
            <h2>用户登录</h2>
            <form id="loginForm">
                <div class="form-group">
                    <label for="loginUsername">用户名:</label>
                    <input type="text" id="loginUsername" name="username" required>
                </div>
                <div class="form-group">
                    <label for="loginPassword">密码:</label>
                    <input type="password" id="loginPassword" name="password" required>
                </div>
                <button type="submit">登录</button>
            </form>
            <div id="loginMessage"></div>
            
            <h3>测试账户</h3>
            <p>管理员: admin / admin123</p>
            <p>普通用户: testuser / user123</p>
        </div>
        
        <!-- 注册表单 -->
        <div id="register" class="tab-content">
            <h2>用户注册</h2>
            <form id="registerForm">
                <div class="form-group">
                    <label for="username">用户名:</label>
                    <input type="text" id="username" name="username" required>
                </div>
                <div class="form-group">
                    <label for="nickname">昵称:</label>
                    <input type="text" id="nickname" name="nickname" required>
                </div>
                <div class="form-group">
                    <label for="password">密码:</label>
                    <input type="password" id="password" name="password" required>
                </div>
                <div class="form-group">
                    <label for="email">邮箱:</label>
                    <input type="email" id="email" name="email" required>
                </div>
                <div class="form-group">
                    <label for="phone">手机号:</label>
                    <input type="tel" id="phone" name="phone">
                </div>
                <div class="form-group">
                    <label for="gender">性别:</label>
                    <select id="gender" name="gender">
                        <option value="">请选择</option>
                        <option value="MALE">男</option>
                        <option value="FEMALE">女</option>
                        <option value="OTHER">其他</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="birthDate">出生日期:</label>
                    <input type="date" id="birthDate" name="birthDate">
                </div>
                <button type="submit">注册</button>
            </form>
            <div id="registerMessage"></div>
        </div>
    </div>

    <script>
        function showTab(tabName) {
            // 隐藏所有标签内容
            const contents = document.querySelectorAll('.tab-content');
            contents.forEach(content => content.classList.remove('active'));
            
            // 移除所有标签的active类
            const tabs = document.querySelectorAll('.tab');
            tabs.forEach(tab => tab.classList.remove('active'));
            
            // 显示选中的标签内容
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');
        }

        // 登录表单提交
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const data = Object.fromEntries(formData);
            
            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                const messageDiv = document.getElementById('loginMessage');
                
                if (result.success) {
                    messageDiv.innerHTML = `<div class="success">登录成功！欢迎 ${result.data.nickname}</div>`;
                    messageDiv.innerHTML += `<div class="success">用户角色: ${result.data.roles.map(r => r.description).join(', ')}</div>`;
                } else {
                    messageDiv.innerHTML = `<div class="error">${result.message}</div>`;
                }
            } catch (error) {
                document.getElementById('loginMessage').innerHTML = `<div class="error">登录失败: ${error.message}</div>`;
            }
        });

        // 注册表单提交
        document.getElementById('registerForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const data = Object.fromEntries(formData);
            
            try {
                const response = await fetch('/api/auth/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                const messageDiv = document.getElementById('registerMessage');
                
                if (result.success) {
                    messageDiv.innerHTML = `<div class="success">注册成功！欢迎 ${result.data.nickname}</div>`;
                    this.reset();
                } else {
                    if (result.data && typeof result.data === 'object') {
                        let errorMsg = result.message + '<br>';
                        for (const [field, error] of Object.entries(result.data)) {
                            errorMsg += `${field}: ${error}<br>`;
                        }
                        messageDiv.innerHTML = `<div class="error">${errorMsg}</div>`;
                    } else {
                        messageDiv.innerHTML = `<div class="error">${result.message}</div>`;
                    }
                }
            } catch (error) {
                document.getElementById('registerMessage').innerHTML = `<div class="error">注册失败: ${error.message}</div>`;
            }
        });
    </script>
</body>
</html>
